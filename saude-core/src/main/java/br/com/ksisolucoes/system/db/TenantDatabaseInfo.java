package br.com.ksisolucoes.system.db;

import br.com.celk.system.LoadTenantRegistry;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.log.Loggable;

import javax.annotation.Resource;
import javax.ejb.Stateless;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

@Stateless
public class TenantDatabaseInfo {

    @Resource(lookup = "java:/jboss/datasources/leitura/localhost-ds")
    private DataSource localhostDS;

    @Resource(lookup = "java:/jboss/datasources/leitura/hospital-ds")
    private DataSource hospitalDS;

    public DatabaseInfo getCurrentTenantDatabaseInfo() {
        String tenant = TenantContext.getContext();
        DataSource ds = getDataSourceForTenant(tenant);

        try (Connection conn = ds.getConnection()) {
            return extractDatabaseInfo(conn);
        } catch (SQLException e) {
            Loggable.log.error("Erro ao obter informações do database", e);
            return null;
        }
    }

    private DatabaseInfo extractDatabaseInfo(Connection conn) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        String url = metaData.getURL();

        String[] urlParts = url.split("//")[1].split("/");
        String[] hostPort = urlParts[0].split(":");

        return new DatabaseInfo(
                hostPort[0], // host
                Integer.parseInt(hostPort[1]), // port
                urlParts[1], // database
                metaData.getUserName() // user
        );
    }

    private DataSource getDataSourceForTenant(String tenant) {
        String resolvedTenant = LoadTenantRegistry.resolver(tenant);

        try {
            String jndiName = "java:/jboss/datasources/leitura/" + resolvedTenant + "-ds";
            InitialContext ctx = new InitialContext();
            return (DataSource) ctx.lookup(jndiName);
        } catch (NamingException e) {
            Loggable.log.warn("DataSource não encontrado para tenant: " + tenant + ", usando localhost", e);
            return localhostDS;
        }
    }
}
