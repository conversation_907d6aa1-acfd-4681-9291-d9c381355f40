package br.com.ksisolucoes.system.db;

public class DatabaseInfo {
    private String host;
    private int port;
    private String database;
    private String username;

    public DatabaseInfo(String s, int i, String urlPart, String userName) {
        this.host = s;
        this.port = i;
        this.database = urlPart;
        this.username = userName;
    }

    public DatabaseInfo(String hostPortPart, int i, String substring) {
        this.host = hostPortPart;
        this.port = i;
        this.database = substring;
    }

    // constructors, getters, setters

    @Override
    public String toString() {
        return String.format("%s:%d/%s (user: %s)", host, port, database, username);
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}