package br.com.ksisolucoes.system.db;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

public class DatabaseHostExtractor {
    
    public String getCurrentDatabaseHost(Connection conn) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        String url = metaData.getURL();
        
        // Extrai host da URL: ************************************
        String[] parts = url.split("//")[1].split("/")[0].split(":");
        return parts[0]; // Retorna apenas o host
    }
    
    public DatabaseInfo getDatabaseInfo(Connection conn) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        String url = metaData.getURL();
        
        // Parse da URL completa
        String hostPort = url.split("//")[1].split("/")[0];
        String[] hostPortParts = hostPort.split(":");
        
        return new DatabaseInfo(
            hostPortParts[0], // host
            Integer.parseInt(hostPortParts[1]), // port
            url.substring(url.lastIndexOf("/") + 1) // database
        );
    }
}